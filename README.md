# 🎓 Campus Ambassador Program

A modern, full-stack web application for managing campus ambassador registrations with referral tracking, admin dashboard, and comprehensive analytics.

## ✨ Features

### 🚀 Core Functionality
- **Campus Ambassador Registration** with form validation
- **Referral System** with automatic tracking and rewards
- **Admin Dashboard** with comprehensive management tools
- **Real-time Statistics** and analytics
- **Responsive Design** for all devices
- **Professional UI** with smooth animations

### 🔐 Security & Validation
- Input validation and sanitization
- Email format validation
- Duplicate registration prevention
- Secure admin authentication
- CORS protection

### 📊 Analytics & Reporting
- Registration statistics
- Referral tracking and counts
- Status management (pending/active/rejected)
- Real-time dashboard updates

## 🛠️ Tech Stack

### Frontend
- **Next.js 13.5.1** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component library
- **Framer Motion** - Smooth animations
- **React Hook Form** - Form handling with validation
- **Zod** - Schema validation

### Backend
- **Next.js API Routes** - Server-side API endpoints
- **PostgreSQL** - Production database with direct connection
- **pg** - PostgreSQL client for Node.js

### Database
- **PostgreSQL 15** - Primary database
- **pgAdmin** - Database administration (optional)
- **Docker Compose** - Easy database setup

## 🚀 Quick Start

### Option 1: Complete Automated Setup (Recommended)

**One command to set up everything:**
```bash
./setup-and-run.sh
```

This script will:
- ✅ Check all prerequisites (Node.js, PostgreSQL, etc.)
- ✅ Start PostgreSQL (Docker or local service)
- ✅ Install all dependencies
- ✅ Create database and user
- ✅ Initialize database schema
- ✅ Start the application

### Option 2: Quick Start (if PostgreSQL is already running)

```bash
./quick-start.sh
```

### Option 3: Manual Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd campus_ambassador_full
   ```

2. **Start PostgreSQL**
   ```bash
   # Option A: With Docker
   docker-compose up -d postgres

   # Option B: Local PostgreSQL
   sudo systemctl start postgresql
   ```

3. **Install dependencies**
   ```bash
   npm install
   ```

4. **Setup database**
   ```bash
   ./setup-database.sh
   ```

5. **Start the application**
   ```bash
   npm run dev
   ```

### 🎯 Access Points
- **Application**: [http://localhost:3000](http://localhost:3000)
- **Admin Dashboard**: [http://localhost:3000/admin](http://localhost:3000/admin)

### Option 2: Manual PostgreSQL Setup

1. **Install PostgreSQL**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # macOS
   brew install postgresql
   
   # Windows
   # Download from https://www.postgresql.org/download/windows/
   ```

2. **Create database and user**
   ```bash
   sudo -u postgres psql
   CREATE DATABASE campus_ambassador_db;
   CREATE USER campus_user WITH PASSWORD 'campus_password';
   GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
   \q
   ```

3. **Follow steps 3-8 from Option 1**

## 📁 Project Structure

```
campus_ambassador_full/
├── src/                          # Frontend source code
│   ├── app/                      # Next.js app directory
│   │   ├── page.tsx             # Landing page
│   │   ├── register/            # Registration page
│   │   ├── success/             # Success page
│   │   └── admin/               # Admin dashboard
│   ├── components/              # Reusable components
│   │   └── ui/                  # UI component library
│   ├── lib/                     # Utilities and API client
│   └── types/                   # TypeScript type definitions
├── backend/                     # Backend source code
│   ├── main.py                  # FastAPI application
│   ├── database_manager.py      # Database utilities
│   ├── setup_database.py        # Database setup script
│   ├── requirements.txt         # Python dependencies
│   └── .env                     # Environment variables
├── docker-compose.yml           # Docker services
├── package.json                 # Node.js dependencies
└── README.md                    # This file
```

## 🔧 Configuration

### Environment Variables

Create `backend/.env` file:

```env
# Database Configuration
DATABASE_URL=postgresql://campus_user:campus_password@localhost:5432/campus_ambassador_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=campus_ambassador_db
DB_USER=campus_user
DB_PASSWORD=campus_password

# Application Settings
ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DEBUG=true

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

## 📡 API Documentation

### Campus Ambassador Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/ca/register` | Register new campus ambassador |
| `GET` | `/admin/registrations` | Get all registrations |
| `PUT` | `/admin/ca/{ca_id}/status` | Update CA status |
| `GET` | `/admin/stats` | Get registration statistics |
| `POST` | `/admin/login` | Admin authentication |

### Example API Usage

```bash
# Register new CA
curl -X POST http://localhost:8000/ca/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "mobile": "1234567890",
    "college": "Example University",
    "branch": "Computer Science",
    "year_of_study": "3rd Year"
  }'

# Get statistics
curl -X GET http://localhost:8000/admin/stats
```

## 👨‍💼 Admin Access

- **Username**: `admin`
- **Password**: `admin123`
- **Dashboard**: [http://localhost:3000/admin](http://localhost:3000/admin)

### Admin Features
- View all registrations
- Update CA status (pending → active/rejected)
- View detailed statistics
- Export data capabilities

## 🗄️ Database Management

### Using Database Manager

```bash
cd backend

# Setup database and user
python database_manager.py setup

# Create tables
python database_manager.py create

# Test connection
python database_manager.py connect

# View statistics
python database_manager.py stats

# Reset database (caution: deletes all data)
python database_manager.py reset
```

## 🛠️ Available Scripts

### Setup & Run Scripts
```bash
npm run setup          # Complete automated setup and run
npm run quick-start     # Quick start (requires PostgreSQL running)
npm run dev            # Start development server only
npm run build          # Build for production
npm run start          # Start production server
```

### Database Scripts
```bash
npm run postgres       # Start PostgreSQL with Docker
npm run postgres:stop  # Stop PostgreSQL Docker container
npm run db:setup       # Setup database and schema
npm run db:init        # Initialize database schema only
npm run db:reset       # Reset database (⚠️ deletes all data)
```

### Development Scripts
```bash
npm run lint           # Check code style
npm run type-check     # TypeScript type checking
```

## 🧪 Testing

### Manual Testing
- **Registration flow**: [http://localhost:3000/register](http://localhost:3000/register)
- **Admin dashboard**: [http://localhost:3000/admin](http://localhost:3000/admin)
- **API endpoints**: Available at `/api/*` routes

## 👨‍💼 Admin Access

- **Username**: `admin`
- **Password**: `admin123`

## 📝 License

MIT License
