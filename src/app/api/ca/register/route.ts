import { NextRequest, NextResponse } from 'next/server';
import { CampusAmbassadorDB, ActivityLogger } from '@/lib/simple-database';
import { z } from 'zod';

// Validation schema
const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  mobile: z.string().min(10, 'Mobile number must be at least 10 digits'),
  college: z.string().min(2, 'College name is required'),
  branch: z.string().min(2, 'Branch is required'),
  year_of_study: z.string().min(1, 'Year of study is required'),
  linkedin: z.string().optional(),
  instagram: z.string().optional(),
  referral_code: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = registerSchema.parse(body);
    
    // Register campus ambassador
    const newCA = await CampusAmbassadorDB.register(validatedData);
    
    // Log activity
    await ActivityLogger.log(
      newCA.ca_id,
      'REGISTRATION',
      'New campus ambassador registered',
      request.ip,
      request.headers.get('user-agent') || undefined
    );
    
    return NextResponse.json({
      success: true,
      message: 'Registration successful',
      data: {
        id: newCA.id,
        ca_id: newCA.ca_id,
        name: newCA.name,
        email: newCA.email,
        mobile: newCA.mobile,
        college: newCA.college,
        branch: newCA.branch,
        year_of_study: newCA.year_of_study,
        status: newCA.status,
        created_at: newCA.created_at,
      },
    });
  } catch (error: any) {
    console.error('Registration error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: 'Validation error',
          errors: error.errors,
        },
        { status: 400 }
      );
    }
    
    if (error.message === 'Email already registered') {
      return NextResponse.json(
        {
          success: false,
          message: 'Email already registered',
        },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    
    let campusAmbassadors;
    
    if (search) {
      campusAmbassadors = await CampusAmbassadorDB.search(search, limit);
    } else {
      campusAmbassadors = await CampusAmbassadorDB.getAll(limit, offset);
    }
    
    return NextResponse.json({
      success: true,
      data: campusAmbassadors,
      pagination: {
        limit,
        offset,
        total: campusAmbassadors.length,
      },
    });
  } catch (error) {
    console.error('Get campus ambassadors error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch campus ambassadors',
      },
      { status: 500 }
    );
  }
}
