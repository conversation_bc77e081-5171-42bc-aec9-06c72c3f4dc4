import { NextRequest, NextResponse } from 'next/server';
import { CampusAmbassadorDB } from '@/lib/simple-database';
import { verifyAdminToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const admin = await verifyAdminToken(request);
    if (!admin) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
        },
        { status: 401 }
      );
    }
    
    // Get statistics
    const stats = await CampusAmbassadorDB.getStats();
    
    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Stats error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch statistics',
      },
      { status: 500 }
    );
  }
}
