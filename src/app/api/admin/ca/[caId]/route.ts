import { NextRequest, NextResponse } from 'next/server';
import { CampusAmbassadorDB, ActivityLogger } from '@/lib/simple-database';
import { verifyAdminToken } from '@/lib/auth';
import { z } from 'zod';

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'active', 'rejected']),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { caId: string } }
) {
  try {
    // Verify admin authentication
    const admin = await verifyAdminToken(request);
    if (!admin) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
        },
        { status: 401 }
      );
    }
    
    const { caId } = params;
    
    // Get campus ambassador details
    const ca = await CampusAmbassadorDB.getById(caId);
    
    if (!ca) {
      return NextResponse.json(
        {
          success: false,
          message: 'Campus ambassador not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: ca,
    });
  } catch (error) {
    console.error('Get CA error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch campus ambassador',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { caId: string } }
) {
  try {
    // Verify admin authentication
    const admin = await verifyAdminToken(request);
    if (!admin) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
        },
        { status: 401 }
      );
    }
    
    const { caId } = params;
    const body = await request.json();
    
    // Validate input
    const { status } = updateStatusSchema.parse(body);
    
    // Update campus ambassador status
    const updatedCA = await CampusAmbassadorDB.updateStatus(caId, status);
    
    if (!updatedCA) {
      return NextResponse.json(
        {
          success: false,
          message: 'Campus ambassador not found',
        },
        { status: 404 }
      );
    }
    
    // Log activity
    await ActivityLogger.log(
      caId,
      'STATUS_UPDATE',
      `Status updated to ${status} by admin ${admin.username}`,
      request.ip,
      request.headers.get('user-agent') || undefined
    );
    
    return NextResponse.json({
      success: true,
      message: 'Status updated successfully',
      data: updatedCA,
    });
  } catch (error: any) {
    console.error('Update CA status error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: 'Validation error',
          errors: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update status',
      },
      { status: 500 }
    );
  }
}
