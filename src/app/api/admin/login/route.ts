import { NextRequest, NextResponse } from 'next/server';
import { AdminDB } from '@/lib/simple-database';
import { generateSessionToken, clearSession } from '@/lib/auth';
import { z } from 'zod';

const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});



export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const { username, password } = loginSchema.parse(body);
    
    // Authenticate admin
    const admin = await AdminDB.authenticate(username, password);
    
    if (!admin) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid credentials',
        },
        { status: 401 }
      );
    }
    
    // Generate session token
    const sessionToken = generateSessionToken({
      id: admin.id,
      username: admin.username,
      role: admin.role,
    });

    // Create response
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      data: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        last_login: admin.last_login,
      },
    });

    // Set HTTP-only cookie
    response.cookies.set('admin_session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
    });
    
    return response;
  } catch (error: any) {
    console.error('Login error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: 'Validation error',
          errors: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get session token and clear it
    const sessionToken = request.cookies.get('admin_session')?.value;
    if (sessionToken) {
      clearSession(sessionToken);
    }

    // Logout - clear the cookie
    const response = NextResponse.json({
      success: true,
      message: 'Logout successful',
    });

    response.cookies.delete('admin_session');

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Logout failed',
      },
      { status: 500 }
    );
  }
}
