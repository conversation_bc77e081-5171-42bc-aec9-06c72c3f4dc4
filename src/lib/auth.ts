import { NextRequest } from 'next/server';

// Simple session storage for demo
const adminSessions = new Map<string, { id: number; username: string; role: string; expires: number }>();

export interface AdminUser {
  id: number;
  username: string;
  role: string;
}

export async function verifyAdminToken(request: NextRequest): Promise<AdminUser | null> {
  try {
    // Get session token from cookie
    const sessionToken = request.cookies.get('admin_session')?.value;

    if (!sessionToken) {
      return null;
    }

    // Check session
    const session = adminSessions.get(sessionToken);
    if (!session || session.expires < Date.now()) {
      adminSessions.delete(sessionToken);
      return null;
    }

    return {
      id: session.id,
      username: session.username,
      role: session.role,
    };
  } catch (error) {
    console.error('Session verification error:', error);
    return null;
  }
}

export function generateSessionToken(admin: AdminUser): string {
  const sessionToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
  const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

  adminSessions.set(sessionToken, {
    id: admin.id,
    username: admin.username,
    role: admin.role,
    expires,
  });

  return sessionToken;
}

export function clearSession(sessionToken: string): void {
  adminSessions.delete(sessionToken);
}
