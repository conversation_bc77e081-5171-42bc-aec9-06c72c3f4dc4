// Simple in-memory database for Campus Ambassador application
// This works without any database setup

interface CARegistration {
  id: number;
  ca_id: string;
  name: string;
  email: string;
  mobile: string;
  college: string;
  branch: string;
  year_of_study: string;
  linkedin?: string;
  instagram?: string;
  status: 'pending' | 'active' | 'rejected';
  referral_code?: string;
  referral_count: number;
  referred_by?: string;
  created_at: string;
  updated_at: string;
}

interface AdminUser {
  id: number;
  username: string;
  password_hash: string;
  email: string;
  role: string;
  created_at: string;
  last_login?: string;
}

interface ActivityLog {
  id: number;
  ca_id?: string;
  action: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

// In-memory storage
let campusAmbassadors: CARegistration[] = [];
let adminUsers: AdminUser[] = [
  {
    id: 1,
    username: 'admin',
    password_hash: 'admin123', // In production, this should be hashed
    email: '<EMAIL>',
    role: 'super_admin',
    created_at: new Date().toISOString(),
  }
];
let activityLogs: ActivityLog[] = [];
let nextCAId = 1;
let nextLogId = 1;

// Campus Ambassador database operations
export class CampusAmbassadorDB {
  // Register new campus ambassador
  static async register(data: {
    name: string;
    email: string;
    mobile: string;
    college: string;
    branch: string;
    year_of_study: string;
    linkedin?: string;
    instagram?: string;
    referral_code?: string;
  }) {
    const {
      name,
      email,
      mobile,
      college,
      branch,
      year_of_study,
      linkedin,
      instagram,
      referral_code,
    } = data;

    // Check if email already exists
    const existingUser = campusAmbassadors.find(ca => ca.email === email);
    if (existingUser) {
      throw new Error('Email already registered');
    }

    // Find referrer if referral code provided
    let referredBy = undefined;
    if (referral_code) {
      const referrer = campusAmbassadors.find(ca => 
        ca.ca_id === referral_code || ca.referral_code === referral_code
      );
      if (referrer) {
        referredBy = referrer.ca_id;
        // Update referrer's count
        referrer.referral_count += 1;
      }
    }

    // Generate CA ID
    const caId = `CA${String(nextCAId).padStart(3, '0')}`;
    nextCAId++;

    // Create new campus ambassador
    const newCA: CARegistration = {
      id: nextCAId,
      ca_id: caId,
      name,
      email,
      mobile,
      college,
      branch,
      year_of_study,
      linkedin,
      instagram,
      status: 'pending',
      referral_code: caId, // Use CA ID as referral code
      referral_count: 0,
      referred_by: referredBy,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    campusAmbassadors.push(newCA);
    return newCA;
  }

  // Get all campus ambassadors
  static async getAll(limit = 100, offset = 0) {
    const start = offset;
    const end = offset + limit;
    return campusAmbassadors
      .slice(start, end)
      .map(ca => ({
        ...ca,
        total_referrals: ca.referral_count,
        referred_by_name: ca.referred_by ? 
          campusAmbassadors.find(ref => ref.ca_id === ca.referred_by)?.name : null
      }));
  }

  // Get campus ambassador by ID
  static async getById(caId: string) {
    const ca = campusAmbassadors.find(ca => ca.ca_id === caId);
    if (!ca) return null;

    return {
      ...ca,
      total_referrals: ca.referral_count,
      referred_by_name: ca.referred_by ? 
        campusAmbassadors.find(ref => ref.ca_id === ca.referred_by)?.name : null
    };
  }

  // Update campus ambassador status
  static async updateStatus(caId: string, status: 'pending' | 'active' | 'rejected') {
    const ca = campusAmbassadors.find(ca => ca.ca_id === caId);
    if (!ca) return null;

    ca.status = status;
    ca.updated_at = new Date().toISOString();
    return ca;
  }

  // Get statistics
  static async getStats() {
    const total = campusAmbassadors.length;
    const active = campusAmbassadors.filter(ca => ca.status === 'active').length;
    const pending = campusAmbassadors.filter(ca => ca.status === 'pending').length;
    const rejected = campusAmbassadors.filter(ca => ca.status === 'rejected').length;
    const totalReferrals = campusAmbassadors.reduce((sum, ca) => sum + ca.referral_count, 0);

    return {
      total,
      active,
      pending,
      rejected,
      total_referrals: totalReferrals,
    };
  }

  // Search campus ambassadors
  static async search(query: string, limit = 50) {
    const searchTerm = query.toLowerCase();
    return campusAmbassadors
      .filter(ca => 
        ca.name.toLowerCase().includes(searchTerm) ||
        ca.email.toLowerCase().includes(searchTerm) ||
        ca.college.toLowerCase().includes(searchTerm) ||
        ca.ca_id.toLowerCase().includes(searchTerm)
      )
      .slice(0, limit)
      .map(ca => ({
        ...ca,
        total_referrals: ca.referral_count,
        referred_by_name: ca.referred_by ? 
          campusAmbassadors.find(ref => ref.ca_id === ca.referred_by)?.name : null
      }));
  }
}

// Admin database operations
export class AdminDB {
  // Authenticate admin
  static async authenticate(username: string, password: string) {
    const admin = adminUsers.find(admin => admin.username === username);
    if (!admin) return null;

    // Simple password check (in production, use proper hashing)
    if (password === admin.password_hash) {
      // Update last login
      admin.last_login = new Date().toISOString();
      return admin;
    }

    return null;
  }
}

// Activity logging
export class ActivityLogger {
  static async log(caId: string, action: string, description?: string, ipAddress?: string, userAgent?: string) {
    const log: ActivityLog = {
      id: nextLogId++,
      ca_id: caId,
      action,
      description,
      ip_address: ipAddress,
      user_agent: userAgent,
      created_at: new Date().toISOString(),
    };
    
    activityLogs.push(log);
  }
}

// Export for debugging
export const getStorageData = () => ({
  campusAmbassadors,
  adminUsers,
  activityLogs,
});
