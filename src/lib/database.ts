import Database from 'better-sqlite3';
import path from 'path';

// Database configuration - using SQLite for simplicity
const dbPath = path.join(process.cwd(), 'campus_ambassador.db');
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Initialize database tables
const initDB = () => {
  // Create tables
  db.exec(`
    CREATE TABLE IF NOT EXISTS campus_ambassadors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      ca_id TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      mobile TEXT NOT NULL,
      college TEXT NOT NULL,
      branch TEXT NOT NULL,
      year_of_study TEXT NOT NULL,
      linkedin TEXT,
      instagram TEXT,
      status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'rejected')),
      referral_code TEXT,
      referral_count INTEGER DEFAULT 0,
      referred_by TEX<PERSON>,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS admin_users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login DATETIME
    );

    CREATE TABLE IF NOT EXISTS activity_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      ca_id TEXT,
      action TEXT NOT NULL,
      description TEXT,
      ip_address TEXT,
      user_agent TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS referrals (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      referrer_ca_id TEXT,
      referred_ca_id TEXT,
      referral_code TEXT NOT NULL,
      status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    -- Insert default admin user (password: admin123)
    INSERT OR IGNORE INTO admin_users (username, password_hash, email, role)
    VALUES ('admin', 'admin123', '<EMAIL>', 'super_admin');
  `);
};

// Initialize database
initDB();

// Database wrapper class
export class DatabaseWrapper {
  // Execute a query
  query(sql: string, params: any[] = []): any {
    try {
      const stmt = db.prepare(sql);
      if (sql.trim().toUpperCase().startsWith('SELECT')) {
        return { rows: stmt.all(params) };
      } else {
        const result = stmt.run(params);
        return { rows: [result] };
      }
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Test database connection
  testConnection(): boolean {
    try {
      const result = this.query('SELECT datetime() as now');
      console.log('Database connected successfully:', result.rows[0]);
      return true;
    } catch (error) {
      console.error('Database connection failed:', error);
      return false;
    }
  }

  // Close connection
  close(): void {
    db.close();
  }
}

// Export singleton instance
export const dbInstance = new DatabaseWrapper();

// Campus Ambassador database operations
export class CampusAmbassadorDB {
  // Register new campus ambassador
  static async register(data: {
    name: string;
    email: string;
    mobile: string;
    college: string;
    branch: string;
    year_of_study: string;
    linkedin?: string;
    instagram?: string;
    referral_code?: string;
  }) {
    const {
      name,
      email,
      mobile,
      college,
      branch,
      year_of_study,
      linkedin,
      instagram,
      referral_code,
    } = data;

    // Check if email already exists
    const existingUser = await db.query(
      'SELECT id FROM campus_ambassadors WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      throw new Error('Email already registered');
    }

    // Find referrer if referral code provided
    let referredBy = null;
    if (referral_code) {
      const referrer = await db.query(
        'SELECT ca_id FROM campus_ambassadors WHERE ca_id = $1 OR referral_code = $1',
        [referral_code]
      );
      if (referrer.rows.length > 0) {
        referredBy = referrer.rows[0].ca_id;
      }
    }

    // Insert new campus ambassador
    const result = await db.query(
      `INSERT INTO campus_ambassadors 
       (name, email, mobile, college, branch, year_of_study, linkedin, instagram, referred_by)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING *`,
      [name, email, mobile, college, branch, year_of_study, linkedin, instagram, referredBy]
    );

    return result.rows[0];
  }

  // Get all campus ambassadors
  static async getAll(limit = 100, offset = 0) {
    const result = await db.query(
      `SELECT ca.*, 
              COUNT(ref.id) as total_referrals,
              referrer.name as referred_by_name
       FROM campus_ambassadors ca
       LEFT JOIN referrals ref ON ca.ca_id = ref.referrer_ca_id
       LEFT JOIN campus_ambassadors referrer ON ca.referred_by = referrer.ca_id
       GROUP BY ca.id, referrer.name
       ORDER BY ca.created_at DESC
       LIMIT $1 OFFSET $2`,
      [limit, offset]
    );
    return result.rows;
  }

  // Get campus ambassador by ID
  static async getById(caId: string) {
    const result = await db.query(
      `SELECT ca.*, 
              COUNT(ref.id) as total_referrals,
              referrer.name as referred_by_name
       FROM campus_ambassadors ca
       LEFT JOIN referrals ref ON ca.ca_id = ref.referrer_ca_id
       LEFT JOIN campus_ambassadors referrer ON ca.referred_by = referrer.ca_id
       WHERE ca.ca_id = $1
       GROUP BY ca.id, referrer.name`,
      [caId]
    );
    return result.rows[0];
  }

  // Update campus ambassador status
  static async updateStatus(caId: string, status: 'pending' | 'active' | 'rejected') {
    const result = await db.query(
      'UPDATE campus_ambassadors SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE ca_id = $2 RETURNING *',
      [status, caId]
    );
    return result.rows[0];
  }

  // Get statistics
  static async getStats() {
    const totalResult = await db.query('SELECT COUNT(*) as total FROM campus_ambassadors');
    const activeResult = await db.query("SELECT COUNT(*) as active FROM campus_ambassadors WHERE status = 'active'");
    const pendingResult = await db.query("SELECT COUNT(*) as pending FROM campus_ambassadors WHERE status = 'pending'");
    const rejectedResult = await db.query("SELECT COUNT(*) as rejected FROM campus_ambassadors WHERE status = 'rejected'");
    const totalReferralsResult = await db.query('SELECT SUM(referral_count) as total_referrals FROM campus_ambassadors');

    return {
      total: parseInt(totalResult.rows[0].total),
      active: parseInt(activeResult.rows[0].active),
      pending: parseInt(pendingResult.rows[0].pending),
      rejected: parseInt(rejectedResult.rows[0].rejected),
      total_referrals: parseInt(totalReferralsResult.rows[0].total_referrals || '0'),
    };
  }

  // Search campus ambassadors
  static async search(query: string, limit = 50) {
    const result = await db.query(
      `SELECT * FROM campus_ambassadors 
       WHERE name ILIKE $1 OR email ILIKE $1 OR college ILIKE $1 OR ca_id ILIKE $1
       ORDER BY created_at DESC
       LIMIT $2`,
      [`%${query}%`, limit]
    );
    return result.rows;
  }
}

// Admin database operations
export class AdminDB {
  // Authenticate admin
  static async authenticate(username: string, password: string) {
    const result = await db.query(
      'SELECT * FROM admin_users WHERE username = $1',
      [username]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const admin = result.rows[0];
    // In a real app, you'd use bcrypt to compare passwords
    // For demo purposes, we'll use plain text comparison
    if (password === 'admin123') {
      // Update last login
      await db.query(
        'UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
        [admin.id]
      );
      return admin;
    }

    return null;
  }
}

// Activity logging
export class ActivityLogger {
  static async log(caId: string, action: string, description?: string, ipAddress?: string, userAgent?: string) {
    await db.query(
      'INSERT INTO activity_logs (ca_id, action, description, ip_address, user_agent) VALUES ($1, $2, $3, $4, $5)',
      [caId, action, description, ipAddress, userAgent]
    );
  }
}
