#!/bin/bash

# Campus Ambassador Application - Quick Start Script
# For users who already have PostgreSQL running

echo "🚀 Campus Ambassador - Quick Start"
echo "=================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    print_error "PostgreSQL is not running!"
    print_info "Please start PostgreSQL first:"
    echo "  Option 1: sudo systemctl start postgresql"
    echo "  Option 2: docker-compose up -d postgres"
    echo "  Option 3: ./setup-and-run.sh (for complete setup)"
    exit 1
fi

print_status "PostgreSQL is running"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    npm install
    print_status "Dependencies installed"
fi

# Check if database exists
if ! psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    print_warning "Database not found. Setting up database..."
    
    # Run database setup
    if [ -f "setup-database.sh" ]; then
        ./setup-database.sh
    else
        print_error "Database setup script not found. Please run ./setup-and-run.sh for complete setup"
        exit 1
    fi
fi

print_status "Database is ready"

# Start the application
print_info "Starting Campus Ambassador Application..."
echo ""
print_status "🎉 Application starting at http://localhost:3000"
print_info "Admin Dashboard: http://localhost:3000/admin"
print_info "Admin credentials: admin / admin123"
echo ""
print_warning "Press Ctrl+C to stop the application"
echo ""

npm run dev
