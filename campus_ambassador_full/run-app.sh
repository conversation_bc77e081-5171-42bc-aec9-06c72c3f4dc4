#!/bin/bash

# Campus Ambassador Application - Simple Setup and Run Script

echo "🎓 Campus Ambassador Application Setup"
echo "====================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js from https://nodejs.org/"
    exit 1
fi

print_status "Node.js is installed: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm"
    exit 1
fi

print_status "npm is installed: $(npm --version)"

# Install dependencies
print_info "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

print_status "Dependencies installed successfully"

# Check if PostgreSQL is running
if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    print_status "PostgreSQL is running"
else
    print_warning "PostgreSQL is not running. Trying to start with Docker..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d postgres
        
        # Wait for PostgreSQL to be ready
        print_info "Waiting for PostgreSQL to start..."
        for i in {1..30}; do
            if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                print_status "PostgreSQL is ready!"
                break
            fi
            sleep 2
            echo -n "."
        done
        
        if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
            print_error "PostgreSQL failed to start. Please start PostgreSQL manually:"
            print_info "Option 1: sudo systemctl start postgresql"
            print_info "Option 2: Install and start PostgreSQL locally"
            exit 1
        fi
    else
        print_error "Docker Compose not found and PostgreSQL is not running"
        print_info "Please install PostgreSQL or Docker and try again"
        exit 1
    fi
fi

# Setup database
print_info "Setting up database..."

# Create database and user
psql -h localhost -U postgres -d postgres -c "
CREATE USER campus_user WITH PASSWORD 'campus_password';
CREATE DATABASE campus_ambassador_db OWNER campus_user;
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
" 2>/dev/null || print_warning "Database user/database might already exist"

# Initialize schema
if [ -f "database/schema.sql" ]; then
    psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql
    
    if [ $? -eq 0 ]; then
        print_status "Database schema initialized"
    else
        print_error "Failed to initialize database schema"
        exit 1
    fi
else
    print_error "Database schema file not found: database/schema.sql"
    exit 1
fi

# Test database connection
if psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT COUNT(*) FROM campus_ambassadors;" >/dev/null 2>&1; then
    print_status "Database connection test successful"
else
    print_error "Database connection test failed"
    exit 1
fi

# Start the application
print_info "Starting the application..."
echo ""
print_status "🎉 Campus Ambassador Application is starting!"
echo ""
print_info "Access the application at:"
echo "  📱 Frontend:        http://localhost:3000"
echo "  👨‍💼 Admin Dashboard: http://localhost:3000/admin"
echo ""
print_info "Admin credentials:"
echo "  Username: admin"
echo "  Password: admin123"
echo ""
print_warning "Press Ctrl+C to stop the application"
echo ""

# Start development server
npm run dev
