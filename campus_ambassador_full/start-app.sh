#!/bin/bash

# Campus Ambassador Application - Simple Starter
# Works with any PostgreSQL setup

echo "🎓 Campus Ambassador Application"
echo "==============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    print_info "Install from: https://nodejs.org/"
    exit 1
fi

print_status "Node.js: $(node --version)"

# Install dependencies
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    print_status "Dependencies installed"
fi

# Check if PostgreSQL is available
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL (psql) is not installed"
    print_info "Install PostgreSQL first:"
    echo "  Ubuntu: sudo apt install postgresql postgresql-contrib"
    echo "  macOS:  brew install postgresql"
    exit 1
fi

# Check if PostgreSQL is running
if ! pg_isready >/dev/null 2>&1; then
    print_warning "PostgreSQL is not running"
    print_info "Please start PostgreSQL:"
    echo "  sudo systemctl start postgresql"
    echo "  OR"
    echo "  sudo service postgresql start"
    echo "  OR"
    echo "  brew services start postgresql (macOS)"
    exit 1
fi

print_status "PostgreSQL is running"

# Check if our database exists
DB_EXISTS=false

# Try different ways to check database
if psql -lqt | cut -d \| -f 1 | grep -qw campus_ambassador_db 2>/dev/null; then
    DB_EXISTS=true
elif sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw campus_ambassador_db 2>/dev/null; then
    DB_EXISTS=true
fi

if [ "$DB_EXISTS" = false ]; then
    print_warning "Database 'campus_ambassador_db' not found"
    print_info "Setting up database..."
    
    if [ -f "setup-postgres.sh" ]; then
        ./setup-postgres.sh
        if [ $? -ne 0 ]; then
            print_error "Database setup failed"
            exit 1
        fi
    else
        print_error "Database setup script not found"
        print_info "Please create the database manually:"
        echo ""
        echo "sudo -u postgres psql"
        echo "CREATE USER campus_user WITH PASSWORD 'campus_password';"
        echo "CREATE DATABASE campus_ambassador_db OWNER campus_user;"
        echo "GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;"
        echo "\\q"
        echo ""
        echo "Then initialize schema:"
        echo "psql -U campus_user -d campus_ambassador_db -f database/schema.sql"
        exit 1
    fi
else
    print_status "Database exists"
fi

# Test database connection
CONNECTION_OK=false

# Try different connection methods
if psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    CONNECTION_OK=true
    export PGHOST=localhost
    export PGUSER=campus_user
    export PGDATABASE=campus_ambassador_db
elif psql -U campus_user -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    CONNECTION_OK=true
    export PGUSER=campus_user
    export PGDATABASE=campus_ambassador_db
elif sudo -u postgres psql -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    CONNECTION_OK=true
    print_warning "Using postgres user for database connection"
    export PGUSER=postgres
    export PGDATABASE=campus_ambassador_db
fi

if [ "$CONNECTION_OK" = false ]; then
    print_error "Cannot connect to database"
    print_info "Please check your PostgreSQL configuration"
    print_info "You may need to:"
    echo "  1. Set a password for postgres user"
    echo "  2. Configure pg_hba.conf for local connections"
    echo "  3. Create the campus_user and database manually"
    exit 1
fi

print_status "Database connection successful"

# Start the application
print_info "Starting Campus Ambassador Application..."
echo ""
print_status "🎉 Application starting!"
echo ""
print_info "Access points:"
echo "  📱 Application:     http://localhost:3000"
echo "  👨‍💼 Admin Dashboard: http://localhost:3000/admin"
echo ""
print_info "Admin credentials:"
echo "  Username: admin"
echo "  Password: admin123"
echo ""
print_warning "Press Ctrl+C to stop"
echo ""

# Start development server
npm run dev
