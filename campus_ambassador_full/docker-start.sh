#!/bin/bash

# Campus Ambassador Application - Docker Setup
# No PostgreSQL installation required!

echo "🐳 Campus Ambassador Application - Docker Setup"
echo "=============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    print_info "Install Docker from: https://docs.docker.com/get-docker/"
    exit 1
fi

print_status "Docker is installed"

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed"
    print_info "Install Docker Compose from: https://docs.docker.com/compose/install/"
    exit 1
fi

print_status "Docker Compose is installed"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    print_info "Install Node.js from: https://nodejs.org/"
    exit 1
fi

print_status "Node.js: $(node --version)"

# Install dependencies
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    print_status "Dependencies installed"
fi

# Start PostgreSQL with Docker
print_info "Starting PostgreSQL with Docker..."
docker-compose up -d postgres

if [ $? -ne 0 ]; then
    print_error "Failed to start PostgreSQL container"
    exit 1
fi

print_status "PostgreSQL container started"

# Wait for PostgreSQL to be ready
print_info "Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        print_status "PostgreSQL is ready!"
        break
    fi
    sleep 2
    echo -n "."
done

if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    print_error "PostgreSQL failed to start properly"
    print_info "Check Docker logs: docker-compose logs postgres"
    exit 1
fi

# Initialize database schema
print_info "Initializing database schema..."

if [ -f "database/schema.sql" ]; then
    # Wait a bit more for PostgreSQL to fully initialize
    sleep 5
    
    # Initialize schema
    if psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql 2>/dev/null; then
        print_status "Database schema initialized"
    else
        print_warning "Schema initialization failed, trying alternative method..."
        sleep 5
        if docker exec campus_ambassador_db psql -U campus_user -d campus_ambassador_db -c "$(cat database/schema.sql)" 2>/dev/null; then
            print_status "Database schema initialized (via Docker)"
        else
            print_error "Failed to initialize database schema"
            print_info "You may need to run this manually after the app starts:"
            echo "psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql"
        fi
    fi
else
    print_error "Database schema file not found: database/schema.sql"
    exit 1
fi

# Test database connection
print_info "Testing database connection..."
if psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    print_status "Database connection successful"
else
    print_warning "Direct database connection failed, but Docker container is running"
    print_info "The application should still work"
fi

# Start the application
print_info "Starting Campus Ambassador Application..."
echo ""
print_status "🎉 Application starting!"
echo ""
print_info "Access points:"
echo "  📱 Application:     http://localhost:3000"
echo "  👨‍💼 Admin Dashboard: http://localhost:3000/admin"
echo "  🗄️  pgAdmin:        http://localhost:5050"
echo ""
print_info "Admin credentials:"
echo "  Username: admin"
echo "  Password: admin123"
echo ""
print_info "pgAdmin credentials:"
echo "  Email: <EMAIL>"
echo "  Password: admin123"
echo ""
print_info "Database connection details:"
echo "  Host: localhost"
echo "  Port: 5432"
echo "  Database: campus_ambassador_db"
echo "  User: campus_user"
echo "  Password: campus_password"
echo ""
print_warning "Press Ctrl+C to stop the application"
print_info "To stop all services: docker-compose down"
echo ""

# Start development server
npm run dev
