{"name": "campus-ambassador-program", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "setup": "./setup-and-run.sh", "quick-start": "./quick-start.sh", "postgres": "docker-compose up -d postgres", "postgres:stop": "docker-compose down", "db:init": "psql -h localhost -U postgres -d postgres -f database/schema.sql", "db:reset": "psql -h localhost -U campus_user -d campus_ambassador_db -c 'DROP SCHEMA public CASCADE; CREATE SCHEMA public;' && npm run db:init", "db:setup": "./setup-database.sh"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "next": "13.5.1", "next-seo": "^6.4.0", "pg": "^8.16.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.9.0", "@types/pg": "^8.15.4", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "13.5.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}