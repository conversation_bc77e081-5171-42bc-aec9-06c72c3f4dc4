#!/bin/bash

# Campus Ambassador Application - Complete Setup and Run Script
# This script handles everything: PostgreSQL setup, dependencies, database initialization, and application startup

set -e  # Exit on any error

echo "🎓 Campus Ambassador Application - Complete Setup"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
    echo "----------------------------------------"
}

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "${CYAN}📋 Step: $1${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    local missing_deps=()
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_status "Node.js is installed: $NODE_VERSION"
    else
        missing_deps+=("Node.js")
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_status "npm is installed: $NPM_VERSION"
    else
        missing_deps+=("npm")
    fi
    
    # Check if PostgreSQL is available (either locally or via Docker)
    if command_exists psql; then
        print_status "PostgreSQL client (psql) is available"
    elif command_exists docker; then
        print_status "Docker is available (can use for PostgreSQL)"
    else
        missing_deps+=("PostgreSQL or Docker")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        echo ""
        print_info "Please install the missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            case $dep in
                "Node.js"|"npm")
                    echo "  - Install Node.js from: https://nodejs.org/"
                    ;;
                "PostgreSQL or Docker")
                    echo "  - Install PostgreSQL from: https://postgresql.org/"
                    echo "  - OR install Docker from: https://docker.com/"
                    ;;
            esac
        done
        exit 1
    fi
    
    print_status "All prerequisites are met!"
    echo ""
}

# Setup PostgreSQL
setup_postgresql() {
    print_header "Setting up PostgreSQL"
    
    # Check if PostgreSQL is running locally
    if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        print_status "PostgreSQL is already running locally"
        return 0
    fi
    
    # Try to start PostgreSQL with Docker
    if command_exists docker && command_exists docker-compose; then
        print_step "Starting PostgreSQL with Docker Compose"
        
        if [ -f "docker-compose.yml" ]; then
            docker-compose up -d postgres
            
            # Wait for PostgreSQL to be ready
            print_info "Waiting for PostgreSQL to be ready..."
            for i in {1..30}; do
                if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
                    print_status "PostgreSQL is ready!"
                    return 0
                fi
                sleep 2
                echo -n "."
            done
            
            print_error "PostgreSQL failed to start with Docker"
        else
            print_warning "docker-compose.yml not found"
        fi
    fi
    
    # If Docker didn't work, check if PostgreSQL service can be started
    if command_exists systemctl; then
        print_step "Trying to start PostgreSQL service"
        if sudo systemctl start postgresql 2>/dev/null; then
            print_status "PostgreSQL service started"
            return 0
        fi
    fi
    
    print_error "Could not start PostgreSQL. Please start it manually:"
    print_info "Option 1: sudo systemctl start postgresql"
    print_info "Option 2: docker-compose up -d postgres"
    print_info "Option 3: Start PostgreSQL using your preferred method"
    exit 1
}

# Install dependencies
install_dependencies() {
    print_header "Installing Dependencies"
    
    print_step "Installing Node.js dependencies"
    npm install
    
    print_status "Dependencies installed successfully!"
    echo ""
}

# Setup database
setup_database() {
    print_header "Setting up Database"
    
    print_step "Creating database and user"
    
    # Create database and user using psql
    psql -h localhost -U postgres -d postgres << EOF || {
        print_warning "Failed to connect as postgres user. Trying alternative method..."
        
        # Try with different connection methods
        if psql -h localhost -p 5432 -U postgres -d postgres << EOF2
-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'campus_user') THEN
        CREATE USER campus_user WITH PASSWORD 'campus_password';
        RAISE NOTICE 'User campus_user created';
    ELSE
        RAISE NOTICE 'User campus_user already exists';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE campus_ambassador_db OWNER campus_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'campus_ambassador_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
EOF2
        then
            print_status "Database and user created successfully"
        else
            print_error "Failed to create database. Please run manually:"
            print_info "psql -h localhost -U postgres -c \"CREATE USER campus_user WITH PASSWORD 'campus_password';\""
            print_info "psql -h localhost -U postgres -c \"CREATE DATABASE campus_ambassador_db OWNER campus_user;\""
            exit 1
        fi
    }

-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'campus_user') THEN
        CREATE USER campus_user WITH PASSWORD 'campus_password';
        RAISE NOTICE 'User campus_user created';
    ELSE
        RAISE NOTICE 'User campus_user already exists';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE campus_ambassador_db OWNER campus_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'campus_ambassador_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
EOF
    
    print_step "Initializing database schema"
    
    # Initialize database schema
    if psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql; then
        print_status "Database schema initialized successfully"
    else
        print_error "Failed to initialize database schema"
        print_info "Please check if database/schema.sql exists and is readable"
        exit 1
    fi
    
    print_status "Database setup completed!"
    echo ""
}

# Test database connection
test_database() {
    print_header "Testing Database Connection"
    
    if psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT COUNT(*) FROM campus_ambassadors;" >/dev/null 2>&1; then
        print_status "Database connection test successful!"
    else
        print_error "Database connection test failed"
        exit 1
    fi
    echo ""
}

# Start the application
start_application() {
    print_header "Starting Application"
    
    print_step "Building Next.js application"
    npm run build
    
    print_step "Starting development server"
    print_status "🎉 Application is starting..."
    echo ""
    print_info "Access the application at:"
    echo "  📱 Frontend:        http://localhost:3000"
    echo "  👨‍💼 Admin Dashboard: http://localhost:3000/admin"
    echo ""
    print_info "Admin credentials:"
    echo "  Username: admin"
    echo "  Password: admin123"
    echo ""
    print_info "Database details:"
    echo "  Host: localhost"
    echo "  Port: 5432"
    echo "  Database: campus_ambassador_db"
    echo "  User: campus_user"
    echo "  Password: campus_password"
    echo ""
    print_warning "Press Ctrl+C to stop the application"
    echo ""
    
    # Start the development server
    npm run dev
}

# Main execution
main() {
    print_info "Starting complete setup for Campus Ambassador Application..."
    echo ""
    
    # Run all setup steps
    check_prerequisites
    setup_postgresql
    install_dependencies
    setup_database
    test_database
    start_application
}

# Handle script interruption
cleanup() {
    echo ""
    print_info "Setup interrupted. Cleaning up..."
    
    # Stop any running Docker containers
    if command_exists docker-compose && [ -f "docker-compose.yml" ]; then
        print_info "Stopping Docker containers..."
        docker-compose down >/dev/null 2>&1 || true
    fi
    
    print_status "Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if script is run from correct directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory (where package.json is located)"
    exit 1
fi

# Run main function
main
