#!/bin/bash

# PostgreSQL Setup Script for Campus Ambassador Application
# Handles different PostgreSQL configurations

echo "🗄️ PostgreSQL Setup for Campus Ambassador"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to test PostgreSQL connection
test_postgres_connection() {
    local user=$1
    local db=$2
    
    if [ -z "$user" ]; then
        user="postgres"
    fi
    
    if [ -z "$db" ]; then
        db="postgres"
    fi
    
    # Try different connection methods
    if psql -h localhost -U "$user" -d "$db" -c "SELECT 1;" >/dev/null 2>&1; then
        return 0
    elif psql -U "$user" -d "$db" -c "SELECT 1;" >/dev/null 2>&1; then
        return 0
    elif sudo -u postgres psql -c "SELECT 1;" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to create database and user
create_database() {
    print_info "Creating database and user..."
    
    # Try different methods to create database
    
    # Method 1: Direct psql as postgres user
    if psql -h localhost -U postgres -d postgres -c "
        CREATE USER campus_user WITH PASSWORD 'campus_password';
        CREATE DATABASE campus_ambassador_db OWNER campus_user;
        GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
    " 2>/dev/null; then
        print_status "Database created successfully (Method 1)"
        return 0
    fi
    
    # Method 2: Using sudo -u postgres
    if sudo -u postgres psql -c "
        CREATE USER campus_user WITH PASSWORD 'campus_password';
        CREATE DATABASE campus_ambassador_db OWNER campus_user;
        GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
    " 2>/dev/null; then
        print_status "Database created successfully (Method 2)"
        return 0
    fi
    
    # Method 3: Try without password (peer authentication)
    if psql -U postgres -d postgres -c "
        CREATE USER campus_user WITH PASSWORD 'campus_password';
        CREATE DATABASE campus_ambassador_db OWNER campus_user;
        GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
    " 2>/dev/null; then
        print_status "Database created successfully (Method 3)"
        return 0
    fi
    
    # Method 4: Interactive creation
    print_warning "Automatic database creation failed. Let's try interactive setup..."
    echo ""
    print_info "Please run the following commands manually:"
    echo ""
    echo "sudo -u postgres psql"
    echo "CREATE USER campus_user WITH PASSWORD 'campus_password';"
    echo "CREATE DATABASE campus_ambassador_db OWNER campus_user;"
    echo "GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;"
    echo "\\q"
    echo ""
    read -p "Press Enter after you've run these commands..."
    
    # Test if it worked
    if test_postgres_connection "campus_user" "campus_ambassador_db"; then
        print_status "Database setup completed!"
        return 0
    else
        print_error "Database setup failed"
        return 1
    fi
}

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL is not installed!"
    print_info "Please install PostgreSQL first:"
    echo ""
    echo "Ubuntu/Debian: sudo apt update && sudo apt install postgresql postgresql-contrib"
    echo "CentOS/RHEL:   sudo yum install postgresql postgresql-server"
    echo "macOS:         brew install postgresql"
    echo "Windows:       Download from https://www.postgresql.org/download/windows/"
    echo ""
    exit 1
fi

print_status "PostgreSQL is installed"

# Check if PostgreSQL service is running
if ! pg_isready >/dev/null 2>&1; then
    print_warning "PostgreSQL service is not running. Trying to start it..."
    
    # Try to start PostgreSQL service
    if command -v systemctl &> /dev/null; then
        if sudo systemctl start postgresql 2>/dev/null; then
            print_status "PostgreSQL service started"
        else
            print_warning "Failed to start PostgreSQL with systemctl"
        fi
    elif command -v service &> /dev/null; then
        if sudo service postgresql start 2>/dev/null; then
            print_status "PostgreSQL service started"
        else
            print_warning "Failed to start PostgreSQL with service command"
        fi
    fi
    
    # Check again
    if ! pg_isready >/dev/null 2>&1; then
        print_error "PostgreSQL is still not running"
        print_info "Please start PostgreSQL manually:"
        echo "  sudo systemctl start postgresql"
        echo "  OR"
        echo "  sudo service postgresql start"
        exit 1
    fi
fi

print_status "PostgreSQL service is running"

# Test basic connection
if test_postgres_connection; then
    print_status "Can connect to PostgreSQL"
else
    print_error "Cannot connect to PostgreSQL"
    print_info "This might be due to authentication configuration"
    print_info "You may need to configure pg_hba.conf for local connections"
    exit 1
fi

# Create database and user
if create_database; then
    print_status "Database setup completed successfully!"
else
    print_error "Database setup failed"
    exit 1
fi

# Initialize schema
print_info "Initializing database schema..."

if [ -f "database/schema.sql" ]; then
    # Try different connection methods for schema initialization
    if psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql 2>/dev/null; then
        print_status "Schema initialized successfully"
    elif psql -U campus_user -d campus_ambassador_db -f database/schema.sql 2>/dev/null; then
        print_status "Schema initialized successfully"
    else
        print_error "Failed to initialize schema"
        print_info "You may need to run this manually:"
        echo "psql -U campus_user -d campus_ambassador_db -f database/schema.sql"
        exit 1
    fi
else
    print_error "Schema file not found: database/schema.sql"
    exit 1
fi

# Final test
print_info "Testing final database connection..."
if psql -h localhost -U campus_user -d campus_ambassador_db -c "SELECT COUNT(*) FROM campus_ambassadors;" >/dev/null 2>&1; then
    print_status "✅ Database is ready!"
elif psql -U campus_user -d campus_ambassador_db -c "SELECT COUNT(*) FROM campus_ambassadors;" >/dev/null 2>&1; then
    print_status "✅ Database is ready!"
else
    print_error "Database test failed"
    exit 1
fi

echo ""
print_status "🎉 PostgreSQL setup completed successfully!"
echo ""
print_info "Database details:"
echo "  Host: localhost"
echo "  Database: campus_ambassador_db"
echo "  User: campus_user"
echo "  Password: campus_password"
echo ""
print_info "You can now run the application with: npm run dev"
