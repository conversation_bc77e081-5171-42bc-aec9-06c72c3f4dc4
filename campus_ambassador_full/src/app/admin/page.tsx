'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  UserCheck, 
  Clock, 
  TrendingUp, 
  Eye,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { api, ApiError } from '@/lib/api';
import { CARegistration, CAStats } from '@/types';
import { formatDate } from '@/lib/utils';

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [registrations, setRegistrations] = useState<CARegistration[]>([]);
  const [stats, setStats] = useState<CAStats | null>(null);
  const [loginData, setLoginData] = useState({ userid: '', password: '' });

  useEffect(() => {
    // Check if already authenticated (simple session check)
    const isLoggedIn = sessionStorage.getItem('adminAuthenticated');
    if (isLoggedIn === 'true') {
      setIsAuthenticated(true);
      loadDashboardData();
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await api.adminLogin(loginData);
      
      sessionStorage.setItem('adminAuthenticated', 'true');
      setIsAuthenticated(true);

      alert("Login Successful! Welcome to the admin dashboard!");

      await loadDashboardData();
    } catch (error) {
      if (error instanceof ApiError) {
        alert(`Login Failed: ${error.message}`);
      } else {
        alert("Login Failed: An unexpected error occurred.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const loadDashboardData = async () => {
    setIsLoadingData(true);
    try {
      console.log('Loading dashboard data...');
      const [registrationsData, statsData] = await Promise.all([
        api.getRegistrations(),
        api.getStats(),
      ]);

      console.log('Registrations:', registrationsData);
      console.log('Stats:', statsData);

      setRegistrations(registrationsData);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      alert("Error: Failed to load dashboard data.");
    } finally {
      setIsLoadingData(false);
    }
  };

  const updateCAStatus = async (caId: number, status: string) => {
    try {
      await api.updateCAStatus(caId, status);

      alert(`Status Updated: Campus Ambassador status updated to ${status}.`);

      // Reload data
      await loadDashboardData();
    } catch (error) {
      alert("Update Failed: Failed to update status.");
    }
  };

  const handleLogout = () => {
    sessionStorage.removeItem('adminAuthenticated');
    setIsAuthenticated(false);
    setRegistrations([]);
    setStats(null);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="shadow-xl">
            <CardHeader className="space-y-4">
              <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Link>
              <div className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Admin Login
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Access the Campus Ambassador dashboard
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="userid">Username</Label>
                  <Input
                    id="userid"
                    type="text"
                    value={loginData.userid}
                    onChange={(e) => setLoginData({ ...loginData, userid: e.target.value })}
                    placeholder="Enter username"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    placeholder="Enter password"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    'Login'
                  )}
                </Button>
              </form>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 text-center">
                  <strong>Demo Credentials:</strong><br />
                  Username: admin<br />
                  Password: admin123
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-blue-600 hover:text-blue-700">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={loadDashboardData}
              variant="outline"
              size="sm"
              disabled={isLoadingData}
            >
              {isLoadingData ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                'Refresh Data'
              )}
            </Button>
            <Button onClick={handleLogout} variant="outline">
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        {stats && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total CAs</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total_cas}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active CAs</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.active_cas}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending CAs</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats.pending_cas}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.total_referrals}</div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Registrations Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Campus Ambassador Registrations</CardTitle>
              <CardDescription>
                Manage and review all campus ambassador applications
                {isLoadingData && (
                  <span className="ml-2 text-blue-600">
                    <Loader2 className="w-4 h-4 inline animate-spin mr-1" />
                    Loading...
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">CA ID</th>
                      <th className="text-left p-2">Name</th>
                      <th className="text-left p-2">Email</th>
                      <th className="text-left p-2">College</th>
                      <th className="text-left p-2">Status</th>
                      <th className="text-left p-2">Referrals</th>
                      <th className="text-left p-2">Date</th>
                      <th className="text-left p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {registrations.map((ca) => (
                      <tr key={ca.id} className="border-b hover:bg-gray-50">
                        <td className="p-2 font-medium text-blue-600">{ca.ca_id}</td>
                        <td className="p-2">{ca.name}</td>
                        <td className="p-2 text-sm text-gray-600">{ca.email}</td>
                        <td className="p-2 text-sm">{ca.college}</td>
                        <td className="p-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            ca.status === 'active' ? 'bg-green-100 text-green-800' :
                            ca.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {ca.status}
                          </span>
                        </td>
                        <td className="p-2 text-center">{ca.referral_count}</td>
                        <td className="p-2 text-sm text-gray-600">
                          {ca.created_at ? formatDate(ca.created_at) : 'N/A'}
                        </td>
                        <td className="p-2">
                          <div className="flex space-x-1">
                            {ca.status === 'pending' && (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => updateCAStatus(ca.id, 'active')}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => updateCAStatus(ca.id, 'rejected')}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
