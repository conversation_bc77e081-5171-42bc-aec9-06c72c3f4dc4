'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';


import { registrationSchema, type RegistrationFormData } from '@/lib/validations';
import { api, ApiError } from '@/lib/api';

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
  });

  const mobileValue = watch('mobile');
  const instagramValue = watch('instagram');

  // Auto-format mobile number
  const handleMobileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 10) {
      setValue('mobile', value);
    }
  };

  // Auto-format Instagram handle
  const handleInstagramChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    if (value && !value.startsWith('@')) {
      value = '@' + value;
    }
    setValue('instagram', value);
  };



  const onSubmit = async (data: RegistrationFormData) => {
    setIsLoading(true);

    try {
      // Get referral code from localStorage
      const referralCode = localStorage.getItem('referralCode');

      // Prepare data for API
      const submitData = {
        ...data,
        linkedin: data.linkedin || undefined,
        instagram: data.instagram || undefined,
        referral_code: referralCode || undefined,
      };

      console.log('Submitting registration:', submitData);

      const result = await api.registerCA(submitData);

      // Store CA data for success page
      localStorage.setItem('caData', JSON.stringify(result));

      alert("Registration Successful! Redirecting to your success page...");

      // Redirect to success page
      router.push('/success');

    } catch (error) {
      console.error('Registration error:', error);

      if (error instanceof ApiError) {
        alert(`Registration Failed: ${error.message}`);
      } else {
        alert("Registration Failed: An unexpected error occurred. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <Card className="shadow-xl">
          <CardHeader className="space-y-4">
            <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
            <div className="text-center">
              <CardTitle className="text-3xl font-bold text-gray-900">
                Join as Campus Ambassador
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 mt-2">
                Fill out the form below to start your journey with us
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Name and Email Row */}
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Enter your full name"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">{errors.email.message}</p>
                  )}
                </div>
              </div>

              {/* Mobile Number */}
              <div className="space-y-2">
                <Label htmlFor="mobile">Mobile Number *</Label>
                <Input
                  id="mobile"
                  value={mobileValue || ''}
                  onChange={handleMobileChange}
                  placeholder="Enter 10-digit mobile number"
                  className={errors.mobile ? 'border-red-500' : ''}
                />
                {errors.mobile && (
                  <p className="text-sm text-red-500">{errors.mobile.message}</p>
                )}
              </div>

              {/* College and Branch Row */}
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="college">College/University *</Label>
                  <Input
                    id="college"
                    {...register('college')}
                    placeholder="Enter your college name"
                    className={errors.college ? 'border-red-500' : ''}
                  />
                  {errors.college && (
                    <p className="text-sm text-red-500">{errors.college.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="branch">Branch/Department *</Label>
                  <Input
                    id="branch"
                    {...register('branch')}
                    placeholder="e.g., Computer Science"
                    className={errors.branch ? 'border-red-500' : ''}
                  />
                  {errors.branch && (
                    <p className="text-sm text-red-500">{errors.branch.message}</p>
                  )}
                </div>
              </div>

              {/* Year of Study */}
              <div className="space-y-2">
                <Label htmlFor="year_of_study">Year of Study *</Label>
                <Input
                  id="year_of_study"
                  {...register('year_of_study')}
                  placeholder="e.g., 2nd Year, Final Year"
                  className={errors.year_of_study ? 'border-red-500' : ''}
                />
                {errors.year_of_study && (
                  <p className="text-sm text-red-500">{errors.year_of_study.message}</p>
                )}
              </div>

              {/* LinkedIn and Instagram Row */}
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="linkedin">LinkedIn Profile (Optional)</Label>
                  <Input
                    id="linkedin"
                    {...register('linkedin')}
                    placeholder="https://linkedin.com/in/yourprofile"
                    className={errors.linkedin ? 'border-red-500' : ''}
                  />
                  {errors.linkedin && (
                    <p className="text-sm text-red-500">{errors.linkedin.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instagram">Instagram Handle (Optional)</Label>
                  <Input
                    id="instagram"
                    value={instagramValue || ''}
                    onChange={handleInstagramChange}
                    placeholder="@yourusername"
                    className={errors.instagram ? 'border-red-500' : ''}
                  />
                  {errors.instagram && (
                    <p className="text-sm text-red-500">{errors.instagram.message}</p>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full text-lg py-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Registering...
                  </>
                ) : (
                  'Register as Campus Ambassador'
                )}
              </Button>

              <div className="text-center">
                  <p className="text-sm text-green-600 font-medium">
                    ✅ Email verified successfully!
                  </p>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
