import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Campus Ambassador Program - Secura Trainings',
  description: 'Join the Secura Trainings Campus Ambassador Program. Promote. Earn. Grow.',
  keywords: ['campus ambassador', 'student program', 'internship', 'referral program'],
  authors: [{ name: 'Secura Trainings' }],
  openGraph: {
    title: 'Campus Ambassador Program - Secura Trainings',
    description: 'Join the Secura Trainings Campus Ambassador Program. Promote. Earn. Grow.',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
