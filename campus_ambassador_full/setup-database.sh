#!/bin/bash

# Campus Ambassador Database Setup Script
echo "🗄️ Setting up Campus Ambassador Database..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
    print_error "PostgreSQL is not running. Please start PostgreSQL first."
    print_info "You can start it with: sudo systemctl start postgresql"
    print_info "Or with Docker: docker-compose up -d postgres"
    exit 1
fi

print_status "PostgreSQL is running"

# Create database and user
print_info "Creating database and user..."

# Connect as postgres user and create database/user
psql -h localhost -U postgres -d postgres << EOF
-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'campus_user') THEN
        CREATE USER campus_user WITH PASSWORD 'campus_password';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE campus_ambassador_db OWNER campus_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'campus_ambassador_db')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
EOF

if [ $? -eq 0 ]; then
    print_status "Database and user created successfully"
else
    print_error "Failed to create database and user"
    exit 1
fi

# Initialize database schema
print_info "Initializing database schema..."

psql -h localhost -U campus_user -d campus_ambassador_db -f database/schema.sql

if [ $? -eq 0 ]; then
    print_status "Database schema initialized successfully"
else
    print_error "Failed to initialize database schema"
    exit 1
fi

print_status "🎉 Database setup completed!"
echo ""
print_info "Database Details:"
echo "  Host: localhost"
echo "  Port: 5432"
echo "  Database: campus_ambassador_db"
echo "  User: campus_user"
echo "  Password: campus_password"
echo ""
print_info "You can now start the application with: npm run dev"
