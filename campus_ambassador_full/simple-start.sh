#!/bin/bash

# Campus Ambassador Application - Simple Start
# Works with existing PostgreSQL setup

echo "🎓 Campus Ambassador Application"
echo "==============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi
print_status "Node.js: $(node --version)"

# Check PostgreSQL
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL is not installed"
    exit 1
fi
print_status "PostgreSQL is available"

# Check if PostgreSQL is running
if ! pg_isready >/dev/null 2>&1; then
    print_error "PostgreSQL is not running"
    print_info "Please start PostgreSQL first:"
    echo "  sudo systemctl start postgresql"
    exit 1
fi
print_status "PostgreSQL is running"

# Install dependencies
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    npm install
    print_status "Dependencies installed"
fi

# Try to create database (this might fail if already exists, which is fine)
print_info "Setting up database (if needed)..."

# Try to create user and database - ignore errors if they already exist
sudo -u postgres psql 2>/dev/null << EOF || echo "Database setup may already exist"
CREATE USER campus_user WITH PASSWORD 'campus_password';
CREATE DATABASE campus_ambassador_db OWNER campus_user;
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
EOF

# Initialize schema if database exists
if psql -U campus_user -d campus_ambassador_db -c "SELECT 1;" >/dev/null 2>&1; then
    print_status "Database connection successful"
    
    # Check if tables exist
    if ! psql -U campus_user -d campus_ambassador_db -c "SELECT 1 FROM campus_ambassadors LIMIT 1;" >/dev/null 2>&1; then
        print_info "Initializing database schema..."
        if [ -f "database/schema.sql" ]; then
            psql -U campus_user -d campus_ambassador_db -f database/schema.sql
            print_status "Schema initialized"
        else
            print_warning "Schema file not found, but continuing..."
        fi
    else
        print_status "Database schema already exists"
    fi
else
    print_warning "Could not connect to database, but continuing..."
    print_info "You may need to set up the database manually"
fi

# Start the application
print_info "Starting Campus Ambassador Application..."
echo ""
print_status "🎉 Application starting!"
echo ""
print_info "Access the application at:"
echo "  📱 Frontend:        http://localhost:3000"
echo "  👨‍💼 Admin Dashboard: http://localhost:3000/admin"
echo ""
print_info "Admin credentials:"
echo "  Username: admin"
echo "  Password: admin123"
echo ""
print_warning "Press Ctrl+C to stop the application"
echo ""

# Start development server
npm run dev
