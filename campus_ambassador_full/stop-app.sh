#!/bin/bash

# Campus Ambassador Application Stop Script
# This script stops all running services

echo "🛑 Stopping Campus Ambassador Application"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Stop backend server
stop_backend() {
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            print_info "Stopping backend server (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            print_status "Backend server stopped"
        else
            print_warning "Backend server not running"
        fi
        rm -f .backend.pid
    else
        print_warning "Backend PID file not found"
    fi
}

# Stop frontend server
stop_frontend() {
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            print_info "Stopping frontend server (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
            print_status "Frontend server stopped"
        else
            print_warning "Frontend server not running"
        fi
        rm -f .frontend.pid
    else
        print_warning "Frontend PID file not found"
    fi
}

# Stop Docker services
stop_docker() {
    if command -v docker-compose &> /dev/null; then
        print_info "Stopping Docker services..."
        docker-compose down
        print_status "Docker services stopped"
    else
        print_warning "Docker Compose not found"
    fi
}

# Kill any remaining processes
cleanup_processes() {
    print_info "Cleaning up any remaining processes..."
    
    # Kill any uvicorn processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    
    # Kill any Next.js processes
    pkill -f "next dev" 2>/dev/null || true
    
    print_status "Process cleanup completed"
}

# Main execution
main() {
    stop_backend
    stop_frontend
    stop_docker
    cleanup_processes
    
    # Clean up log files
    print_info "Cleaning up log files..."
    rm -f frontend.log
    rm -f backend/backend.log
    
    print_status "🎉 Application stopped successfully!"
    echo ""
    print_info "All services have been stopped and cleaned up."
}

# Run main function
main
