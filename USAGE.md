# 🎯 Campus Ambassador Application - Usage Guide

## 🚀 Getting Started

### For First-Time Users

**Just run one command:**
```bash
./setup-and-run.sh
```

This will handle everything automatically:
- Check prerequisites
- Start PostgreSQL
- Install dependencies  
- Setup database
- Start the application

### For Subsequent Runs

**If you've already set up once:**
```bash
./quick-start.sh
```

Or use npm scripts:
```bash
npm run quick-start
```

## 📱 Using the Application

### 1. Campus Ambassador Registration

1. **Visit**: http://localhost:3000
2. **Click**: "Become a Campus Ambassador"
3. **Fill the form** with:
   - Personal details (name, email, mobile)
   - Academic info (college, branch, year)
   - Social media (optional)
   - Referral code (optional)
4. **Submit** and get your unique CA ID

### 2. Admin Dashboard

1. **Visit**: http://localhost:3000/admin
2. **Login** with:
   - Username: `admin`
   - Password: `admin123`
3. **Manage**:
   - View all registrations
   - Update CA status (pending → active/rejected)
   - View statistics and analytics
   - Track referrals

## 🗄️ Database Management

### Quick Commands
```bash
# Start PostgreSQL
npm run postgres

# Setup database
npm run db:setup

# Reset database (⚠️ deletes all data)
npm run db:reset

# Stop PostgreSQL
npm run postgres:stop
```

### Manual Database Access
```bash
# Connect to database
psql -h localhost -U campus_user -d campus_ambassador_db

# View tables
\dt

# View campus ambassadors
SELECT * FROM campus_ambassadors;

# View statistics
SELECT status, COUNT(*) FROM campus_ambassadors GROUP BY status;
```

## 🔧 Development

### File Structure
```
src/
├── app/                    # Next.js pages
│   ├── api/               # API routes
│   ├── admin/             # Admin dashboard
│   └── register/          # Registration page
├── components/            # React components
├── lib/                   # Utilities
│   ├── database.ts        # Database connection
│   └── api.ts            # API client
└── types/                 # TypeScript types
```

### Adding New Features

1. **Database changes**: Update `database/schema.sql`
2. **API endpoints**: Add to `src/app/api/`
3. **Frontend pages**: Add to `src/app/`
4. **Components**: Add to `src/components/`

### Environment Variables

Create `.env.local`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=campus_ambassador_db
DB_USER=campus_user
DB_PASSWORD=campus_password
JWT_SECRET=your-secret-key
```

## 🚨 Troubleshooting

### PostgreSQL Issues

**Problem**: "PostgreSQL is not running"
```bash
# Solution 1: Start with Docker
docker-compose up -d postgres

# Solution 2: Start local service
sudo systemctl start postgresql

# Solution 3: Check if running
pg_isready -h localhost -p 5432
```

**Problem**: "Database connection failed"
```bash
# Check if database exists
psql -h localhost -U postgres -l | grep campus_ambassador_db

# Recreate database
npm run db:reset
```

### Application Issues

**Problem**: "Port 3000 already in use"
```bash
# Kill process on port 3000
sudo lsof -ti:3000 | xargs kill -9

# Or use different port
PORT=3001 npm run dev
```

**Problem**: "Module not found"
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### Permission Issues

**Problem**: "Permission denied" for scripts
```bash
# Make scripts executable
chmod +x setup-and-run.sh quick-start.sh setup-database.sh
```

## 📊 Features Overview

### ✅ What's Working
- Campus Ambassador registration
- Admin dashboard with full CRUD
- Referral system with tracking
- Statistics and analytics
- PostgreSQL integration
- Responsive design
- Form validation
- Status management

### 🎯 Key Endpoints
- `GET /` - Landing page
- `GET /register` - Registration form
- `GET /admin` - Admin dashboard
- `POST /api/ca/register` - Register new CA
- `GET /api/admin/stats` - Get statistics
- `PUT /api/admin/ca/[id]` - Update CA status

## 🎉 Success Indicators

When everything is working correctly, you should see:

1. **Application starts** without errors
2. **Database connects** successfully
3. **Registration works** and generates CA ID
4. **Admin dashboard** shows data
5. **Statistics display** correctly

## 📞 Support

If you encounter issues:

1. **Check logs** in the terminal
2. **Verify PostgreSQL** is running
3. **Check database connection** manually
4. **Review environment variables**
5. **Try the reset commands** above

For development questions, check the main README.md file.
