# 🛠️ Development Guide

This guide provides detailed instructions for setting up and developing the Campus Ambassador application.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# Clone and setup everything automatically
./start-app.sh

# Stop everything
./stop-app.sh
```

### Option 2: Manual Setup
```bash
# 1. Start PostgreSQL
npm run postgres

# 2. Setup database
npm run db:setup
npm run db:create

# 3. Install dependencies
npm run install:all

# 4. Start backend
npm run backend

# 5. Start frontend (in new terminal)
npm run dev
```

## 📋 Prerequisites

### Required Software
- **Node.js 18+** - [Download](https://nodejs.org/)
- **Python 3.8+** - [Download](https://python.org/)
- **Docker & Docker Compose** - [Download](https://docker.com/) (for PostgreSQL)

### Optional Software
- **PostgreSQL 15** - If not using Docker
- **pgAdmin** - Database administration tool
- **VS Code** - Recommended IDE with extensions:
  - Python
  - TypeScript and JavaScript
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

## 🗄️ Database Setup

### Using Docker (Recommended)
```bash
# Start PostgreSQL container
docker-compose up -d postgres

# Start pgAdmin (optional)
docker-compose up -d pgadmin

# Access pgAdmin at http://localhost:5050
# Email: <EMAIL>
# Password: admin123
```

### Manual PostgreSQL Installation

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### macOS
```bash
brew install postgresql
brew services start postgresql
```

#### Windows
Download and install from [PostgreSQL official website](https://www.postgresql.org/download/windows/)

### Database Configuration
```bash
# Connect to PostgreSQL
sudo -u postgres psql

# Create database and user
CREATE DATABASE campus_ambassador_db;
CREATE USER campus_user WITH PASSWORD 'campus_password';
GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;
\q
```

## 🔧 Environment Configuration

### Backend Environment (.env)
Create `backend/.env`:
```env
# Database
DATABASE_URL=postgresql://campus_user:campus_password@localhost:5432/campus_ambassador_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=campus_ambassador_db
DB_USER=campus_user
DB_PASSWORD=campus_password

# Application
ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DEBUG=true

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### Frontend Environment (.env.local)
Create `.env.local` (optional):
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=Campus Ambassador Program
```

## 🏗️ Development Workflow

### Starting Development
```bash
# Option 1: Full automated setup
./start-app.sh

# Option 2: Individual services
npm run postgres        # Start PostgreSQL
npm run backend        # Start FastAPI server
npm run dev           # Start Next.js frontend
```

### Database Operations
```bash
npm run db:setup      # Setup database and user
npm run db:create     # Create tables
npm run db:stats      # View statistics
npm run db:reset      # Reset database (⚠️ deletes all data)
```

### Testing
```bash
npm run test:backend  # Run backend tests
npm test             # Run frontend tests
npm run lint         # Check code style
npm run type-check   # TypeScript type checking
```

### Building for Production
```bash
npm run build        # Build frontend
cd backend && python -m build  # Build backend (if needed)
```

## 📁 Project Structure

```
campus_ambassador_full/
├── 📱 Frontend (Next.js)
│   ├── src/app/              # App Router pages
│   ├── src/components/       # React components
│   ├── src/lib/             # Utilities and API client
│   └── src/types/           # TypeScript definitions
├── 🔧 Backend (FastAPI)
│   ├── main.py              # FastAPI application
│   ├── database_manager.py  # Database utilities
│   ├── requirements.txt     # Python dependencies
│   └── .env                 # Environment variables
├── 🗄️ Database
│   ├── docker-compose.yml   # PostgreSQL container
│   └── backend/init.sql     # Database initialization
└── 🚀 Scripts
    ├── start-app.sh         # Automated startup
    └── stop-app.sh          # Automated shutdown
```

## 🔍 Debugging

### Backend Debugging
```bash
# View backend logs
tail -f backend/backend.log

# Debug mode
cd backend
source venv/bin/activate
python -m debugpy --listen 5678 --wait-for-client -m uvicorn main:app --reload
```

### Frontend Debugging
```bash
# View frontend logs
tail -f frontend.log

# Debug in browser
# Open http://localhost:3000
# Use browser dev tools (F12)
```

### Database Debugging
```bash
# Connect to database
psql -h localhost -U campus_user -d campus_ambassador_db

# View tables
\dt

# View data
SELECT * FROM ca_registrations;
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
source venv/bin/activate
pytest -v                    # Verbose output
pytest --cov=main           # With coverage
pytest tests/test_api.py     # Specific test file
```

### Frontend Tests
```bash
npm test                     # Run all tests
npm test -- --watch         # Watch mode
npm test -- --coverage      # With coverage
```

### API Testing
```bash
# Test registration endpoint
curl -X POST http://localhost:8000/ca/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "mobile": "1234567890",
    "college": "Test College",
    "branch": "Computer Science",
    "year_of_study": "3rd Year"
  }'

# Test admin endpoints
curl -X GET http://localhost:8000/admin/stats
```

## 🚀 Deployment

### Development Deployment
```bash
# Using Docker Compose
docker-compose up -d

# Manual deployment
./start-app.sh
```

### Production Deployment
1. Update environment variables for production
2. Build frontend: `npm run build`
3. Setup production database
4. Deploy using your preferred method (Docker, PM2, etc.)

## 🔧 Common Issues & Solutions

### Port Already in Use
```bash
# Kill processes on specific ports
sudo lsof -ti:3000 | xargs kill -9  # Frontend
sudo lsof -ti:8000 | xargs kill -9  # Backend
sudo lsof -ti:5432 | xargs kill -9  # PostgreSQL
```

### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Check database exists
psql -h localhost -U campus_user -l
```

### Python Virtual Environment Issues
```bash
# Recreate virtual environment
cd backend
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Node.js Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Radix UI Documentation](https://www.radix-ui.com/docs)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `npm test && npm run test:backend`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📞 Support

For development support:
- Check this development guide
- Review the main README.md
- Check the API documentation at http://localhost:8000/docs
- Create an issue on GitHub
