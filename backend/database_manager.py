#!/usr/bin/env python3
"""
Database Manager for Campus Ambassador Application
Handles database operations, migrations, and utilities
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Load environment variables
load_dotenv()

# Import models
from main import Base, CARegistration

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://campus_user:campus_password@localhost:5432/campus_ambassador_db")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "campus_ambassador_db")
DB_USER = os.getenv("DB_USER", "campus_user")
DB_PASSWORD = os.getenv("DB_PASSWORD", "campus_password")

class DatabaseManager:
    def __init__(self):
        self.engine = None
        self.session = None
    
    def connect(self):
        """Connect to the database"""
        try:
            self.engine = create_engine(DATABASE_URL)
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            self.session = SessionLocal()
            print("✅ Connected to database successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def create_tables(self):
        """Create all tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            print("✅ Tables created successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to create tables: {e}")
            return False
    
    def drop_tables(self):
        """Drop all tables (use with caution!)"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            print("✅ Tables dropped successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to drop tables: {e}")
            return False
    
    def reset_database(self):
        """Reset database (drop and recreate tables)"""
        print("⚠️  Resetting database - all data will be lost!")
        if self.drop_tables() and self.create_tables():
            print("✅ Database reset completed")
            return True
        return False
    
    def get_stats(self):
        """Get database statistics"""
        try:
            total_cas = self.session.query(CARegistration).count()
            active_cas = self.session.query(CARegistration).filter(CARegistration.status == "active").count()
            pending_cas = self.session.query(CARegistration).filter(CARegistration.status == "pending").count()
            
            print(f"📊 Database Statistics:")
            print(f"   Total CAs: {total_cas}")
            print(f"   Active CAs: {active_cas}")
            print(f"   Pending CAs: {pending_cas}")
            
            return {
                "total": total_cas,
                "active": active_cas,
                "pending": pending_cas
            }
        except Exception as e:
            print(f"❌ Failed to get statistics: {e}")
            return None
    
    def test_connection(self):
        """Test database connection"""
        try:
            result = self.session.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            print(f"✅ Database connection test successful")
            print(f"   PostgreSQL version: {version}")
            return True
        except Exception as e:
            print(f"❌ Database connection test failed: {e}")
            return False
    
    def close(self):
        """Close database connection"""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()
        print("✅ Database connection closed")

def setup_postgres():
    """Setup PostgreSQL database and user"""
    try:
        # Connect to PostgreSQL server
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database="postgres",
            user="postgres",
            password="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create user if not exists
        try:
            cursor.execute(f"CREATE USER {DB_USER} WITH PASSWORD '{DB_PASSWORD}';")
            print(f"✅ Created user: {DB_USER}")
        except psycopg2.errors.DuplicateObject:
            print(f"ℹ️  User {DB_USER} already exists")
        
        # Create database if not exists
        try:
            cursor.execute(f"CREATE DATABASE {DB_NAME} OWNER {DB_USER};")
            print(f"✅ Created database: {DB_NAME}")
        except psycopg2.errors.DuplicateDatabase:
            print(f"ℹ️  Database {DB_NAME} already exists")
        
        # Grant privileges
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {DB_NAME} TO {DB_USER};")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup PostgreSQL: {e}")
        return False

def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python database_manager.py [command]")
        print("Commands:")
        print("  setup     - Setup PostgreSQL database and user")
        print("  connect   - Test database connection")
        print("  create    - Create tables")
        print("  drop      - Drop tables")
        print("  reset     - Reset database (drop and recreate)")
        print("  stats     - Show database statistics")
        return
    
    command = sys.argv[1].lower()
    
    if command == "setup":
        print("🚀 Setting up PostgreSQL...")
        setup_postgres()
    
    elif command in ["connect", "create", "drop", "reset", "stats"]:
        db = DatabaseManager()
        if not db.connect():
            return
        
        if command == "connect":
            db.test_connection()
        elif command == "create":
            db.create_tables()
        elif command == "drop":
            db.drop_tables()
        elif command == "reset":
            db.reset_database()
        elif command == "stats":
            db.get_stats()
        
        db.close()
    
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()
