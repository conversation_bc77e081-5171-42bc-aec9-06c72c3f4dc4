#!/usr/bin/env python3
"""
Database setup script for Campus Ambassador application
Creates PostgreSQL database and user if they don't exist
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "campus_ambassador_db")
DB_USER = os.getenv("DB_USER", "campus_user")
DB_PASSWORD = os.getenv("DB_PASSWORD", "campus_password")

def create_database_and_user():
    """Create database and user if they don't exist"""
    try:
        # Connect to PostgreSQL server (default database)
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database="postgres",  # Connect to default database
            user="postgres",      # Assuming postgres is the superuser
            password="postgres"   # Default password - change as needed
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create user if not exists
        try:
            cursor.execute(f"""
                CREATE USER {DB_USER} WITH PASSWORD '{DB_PASSWORD}';
            """)
            print(f"✅ Created user: {DB_USER}")
        except psycopg2.errors.DuplicateObject:
            print(f"ℹ️  User {DB_USER} already exists")
        
        # Create database if not exists
        try:
            cursor.execute(f"""
                CREATE DATABASE {DB_NAME} OWNER {DB_USER};
            """)
            print(f"✅ Created database: {DB_NAME}")
        except psycopg2.errors.DuplicateDatabase:
            print(f"ℹ️  Database {DB_NAME} already exists")
        
        # Grant privileges
        cursor.execute(f"""
            GRANT ALL PRIVILEGES ON DATABASE {DB_NAME} TO {DB_USER};
        """)
        print(f"✅ Granted privileges to {DB_USER}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Database setup completed!")
        print(f"Database: {DB_NAME}")
        print(f"User: {DB_USER}")
        print(f"Host: {DB_HOST}:{DB_PORT}")
        
    except psycopg2.Error as e:
        print(f"❌ Error setting up database: {e}")
        print("\n💡 Make sure PostgreSQL is running and you have the correct credentials")
        print("   Default connection assumes postgres user with password 'postgres'")
        return False
    
    return True

def test_connection():
    """Test connection to the created database"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ Successfully connected to database!")
        print(f"PostgreSQL version: {version[0]}")
        cursor.close()
        conn.close()
        return True
    except psycopg2.Error as e:
        print(f"❌ Failed to connect to database: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Setting up Campus Ambassador Database...")
    print("=" * 50)
    
    if create_database_and_user():
        print("\n🔍 Testing database connection...")
        test_connection()
    
    print("\n📝 Next steps:")
    print("1. Run: python main.py (to create tables)")
    print("2. Start the server: uvicorn main:app --reload")
