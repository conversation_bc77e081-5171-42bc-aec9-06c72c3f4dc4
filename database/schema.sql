-- Campus Ambassador Database Schema
-- PostgreSQL Database Setup

-- Create database (run this as postgres superuser)
-- CREATE DATABASE campus_ambassador_db;
-- CREATE USER campus_user WITH PASSWORD 'campus_password';
-- GRANT ALL PRIVILEGES ON DATABASE campus_ambassador_db TO campus_user;

-- Connect to campus_ambassador_db and run the following:

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Campus Ambassadors table
CREATE TABLE IF NOT EXISTS campus_ambassadors (
    id SERIAL PRIMARY KEY,
    ca_id VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mobile VARCHAR(20) NOT NULL,
    college VARCHAR(255) NOT NULL,
    branch VARCHAR(255) NOT NULL,
    year_of_study VARCHAR(50) NOT NULL,
    linkedin VARCHAR(255),
    instagram VARCHAR(255),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'rejected')),
    referral_code VARCHAR(50),
    referral_count INTEGER DEFAULT 0,
    referred_by VARCHAR(10) REFERENCES campus_ambassadors(ca_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    id SERIAL PRIMARY KEY,
    ca_id VARCHAR(10) REFERENCES campus_ambassadors(ca_id),
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Referral tracking table
CREATE TABLE IF NOT EXISTS referrals (
    id SERIAL PRIMARY KEY,
    referrer_ca_id VARCHAR(10) REFERENCES campus_ambassadors(ca_id),
    referred_ca_id VARCHAR(10) REFERENCES campus_ambassadors(ca_id),
    referral_code VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_campus_ambassadors_email ON campus_ambassadors(email);
CREATE INDEX IF NOT EXISTS idx_campus_ambassadors_ca_id ON campus_ambassadors(ca_id);
CREATE INDEX IF NOT EXISTS idx_campus_ambassadors_status ON campus_ambassadors(status);
CREATE INDEX IF NOT EXISTS idx_campus_ambassadors_created_at ON campus_ambassadors(created_at);
CREATE INDEX IF NOT EXISTS idx_activity_logs_ca_id ON activity_logs(ca_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_ca_id ON referrals(referrer_ca_id);

-- Function to generate CA ID
CREATE OR REPLACE FUNCTION generate_ca_id()
RETURNS TRIGGER AS $$
BEGIN
    NEW.ca_id := 'CA' || LPAD(NEW.id::TEXT, 3, '0');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate CA ID
CREATE TRIGGER trigger_generate_ca_id
    BEFORE INSERT ON campus_ambassadors
    FOR EACH ROW
    EXECUTE FUNCTION generate_ca_id();

-- Function to update referral count
CREATE OR REPLACE FUNCTION update_referral_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.referred_by IS NOT NULL THEN
        UPDATE campus_ambassadors 
        SET referral_count = referral_count + 1
        WHERE ca_id = NEW.referred_by;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update referral count
CREATE TRIGGER trigger_update_referral_count
    AFTER INSERT ON campus_ambassadors
    FOR EACH ROW
    EXECUTE FUNCTION update_referral_count();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at
CREATE TRIGGER trigger_update_campus_ambassadors_updated_at
    BEFORE UPDATE ON campus_ambassadors
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password_hash, email, role) 
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflLxQjO', '<EMAIL>', 'super_admin')
ON CONFLICT (username) DO NOTHING;

-- Grant permissions to campus_user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO campus_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO campus_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO campus_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO campus_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO campus_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT EXECUTE ON FUNCTIONS TO campus_user;
